#### Install atlas cli

- macOS
```
brew install ariga/tap/atlas
```

- linux
```
curl -sSf https://atlasgo.sh | sh
```

### Migration Strategy

This project follows the **xbit-goback approach** for database migrations:

- **Production**: Uses **GORM AutoMigrate** for automatic schema management
- **Development**: Uses **Atlas migrations** for explicit schema control and reference

### Development flow

#### For Development (Atlas Migrations)
- Use local connection for development.
- Don't apply migration to shared environment, it will be applied by CD or Devops

1. Write your model first, and add it to atlasloader (`cmd/atlasloader/main.go`)
2. Generate migration file (for reference and development)
```bash
$ make db-diff
```

3. Apply your migration to local (development only)
```bash
$ make db-apply
# or directly with Atlas
$ make db-apply-atlas
```

### Troubleshooting

#### Atlas Migration Baseline Issue

If you encounter the error:
```
Error: sql/migrate: connected database is not clean: found schema "public". baseline version or allow-dirty is required
```

This happens when:
- Database already has schema (from GORM AutoMigrate)
- Atlas doesn't know the current version
- `atlas_schema_revisions` table is empty or missing

**Solution:**
```bash
# Fix the baseline issue
$ make db-fix-baseline

# Or run the script directly
$ ./scripts/fix-atlas-baseline.sh
```

This script will:
1. Check if Atlas is properly initialized
2. If database has existing tables but no Atlas baseline, set baseline to latest migration
3. If database is empty, apply migrations normally

#### For Production (GORM AutoMigrate)
- Database schema is automatically managed by GORM AutoMigrate
- No manual migration application needed
- Migration files are kept for reference and development purposes

### Database Schema

This project implements a multi-level referral system with the following tables:

#### users
- `id` (UUID, Primary Key)
- `email` (TEXT, Unique)
- `is_first_login` (BOOLEAN, Default: true)
- `is_exported_wallet` (BOOLEAN, Default: false)
- `invitation_code` (TEXT, 5-15 characters, can be null initially)

#### user_wallets
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key to users.id)
- `chain` (TEXT, Not Null)
- `name` (TEXT)
- `wallet_address` (TEXT, Not Null)
- `wallet_id` (UUID)
- `wallet_account_id` (UUID)
- `wallet_type` (TEXT, CHECK: 'embedded' or 'managed')
- Unique constraint on (wallet_address, chain)

#### referrals
- `id` (SERIAL, Primary Key)
- `user_id` (UUID, Foreign Key to users.id, Unique)
- `referrer_id` (UUID, Foreign Key to users.id)
- `depth` (INT, Default: 1) - 1 means direct referral
- `created_at` (TIMESTAMP, Default: CURRENT_TIMESTAMP)

#### referral_snapshots
- `user_id` (UUID, Primary Key, Foreign Key to users.id)
- `direct_count` (INT, Default: 0)
- `total_downline_count` (INT, Default: 0)
- `total_volume_usd` (NUMERIC(38,2), Default: 0)
- `total_rewards_distributed` (NUMERIC(38,6), Default: 0)

### Sample Query: Get All Downlines (Recursive CTE)
```sql
WITH RECURSIVE downlines AS (
  SELECT user_id, referrer_id, 1 AS depth
  FROM referrals
  WHERE referrer_id = 'REFERRER-UUID-HERE'
  UNION ALL
  SELECT r.user_id, r.referrer_id, d.depth + 1
  FROM referrals r
  INNER JOIN downlines d ON r.referrer_id = d.user_id
)
SELECT * FROM downlines;
```
