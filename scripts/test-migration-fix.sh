#!/bin/bash
set -e

# Test script to verify the Atlas baseline fix works correctly

echo "🧪 Testing Atlas Migration Baseline Fix"
echo "======================================="

# Load environment variables
if [ -f "env/local.env" ]; then
    echo "📋 Loading local environment variables..."
    export $(grep -v '^#' env/local.env | xargs)
else
    echo "❌ ERROR: env/local.env not found"
    exit 1
fi

# Set DATABASE_URL if not already set
if [ -z "$DATABASE_URL" ]; then
    export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB?sslmode=disable"
fi

echo "📊 Testing with DATABASE_URL: $DATABASE_URL"

# Function to check if database is accessible
test_database() {
    echo "🔌 Testing database connection..."
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "✅ Database connection successful"
        return 0
    else
        echo "❌ Database connection failed"
        echo "Please ensure PostgreSQL is running and credentials are correct"
        return 1
    fi
}

# Function to show current database state
show_database_state() {
    echo "📊 Current Database State:"
    echo "=========================="
    
    # Show existing tables
    echo "📋 Existing tables:"
    psql "$DATABASE_URL" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' ORDER BY table_name;" 2>/dev/null || echo "No tables found"
    
    # Show Atlas schema revisions if exists
    echo ""
    echo "📋 Atlas schema revisions:"
    if psql "$DATABASE_URL" -c "SELECT * FROM atlas_schema_revisions ORDER BY version;" 2>/dev/null; then
        echo "Atlas revisions found"
    else
        echo "No Atlas revisions found or table doesn't exist"
    fi
}

# Function to simulate the issue by clearing atlas_schema_revisions
simulate_issue() {
    echo "🔧 Simulating the baseline issue..."
    
    # Drop atlas_schema_revisions table if it exists
    psql "$DATABASE_URL" -c "DROP TABLE IF EXISTS atlas_schema_revisions;" 2>/dev/null || true
    
    echo "✅ Atlas schema revisions table dropped (simulating the issue)"
}

# Function to test the fix
test_fix() {
    echo "🚀 Testing the baseline fix script..."
    
    if ./scripts/fix-atlas-baseline.sh; then
        echo "✅ Baseline fix script executed successfully"
        return 0
    else
        echo "❌ Baseline fix script failed"
        return 1
    fi
}

# Function to verify the fix worked
verify_fix() {
    echo "🔍 Verifying the fix worked..."
    
    # Check if atlas_schema_revisions table now exists and has data
    REVISION_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM atlas_schema_revisions;" 2>/dev/null | tr -d ' ' || echo "0")
    
    if [ "$REVISION_COUNT" -gt "0" ]; then
        echo "✅ Atlas schema revisions table now has $REVISION_COUNT entries"
        echo "📋 Current revisions:"
        psql "$DATABASE_URL" -c "SELECT version, description FROM atlas_schema_revisions ORDER BY version;"
        return 0
    else
        echo "❌ Atlas schema revisions table is still empty or doesn't exist"
        return 1
    fi
}

# Main test execution
main() {
    echo "🏁 Starting migration fix test..."
    
    # Test database connection
    if ! test_database; then
        echo "❌ Cannot proceed without database connection"
        exit 1
    fi
    
    # Show initial state
    echo ""
    echo "📊 Initial State:"
    show_database_state
    
    # Simulate the issue
    echo ""
    simulate_issue
    
    # Show state after simulating issue
    echo ""
    echo "📊 State After Simulating Issue:"
    show_database_state
    
    # Test the fix
    echo ""
    test_fix
    
    # Verify the fix worked
    echo ""
    verify_fix
    
    # Show final state
    echo ""
    echo "📊 Final State:"
    show_database_state
    
    echo ""
    echo "🎉 Migration fix test completed!"
}

# Check if we're in the right directory
if [ ! -f "atlas.hcl" ]; then
    echo "❌ ERROR: atlas.hcl not found. Please run this script from the project root directory."
    exit 1
fi

# Run the test
main "$@"
