#!/bin/bash
set -e

# Script to fix Atlas migration baseline issue
# This script handles the case where database already has schema but Atlas doesn't know the version

echo "🔧 Atlas Baseline Fix Script"
echo "================================"

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    echo "Please set DATABASE_URL like: postgresql://user:pass@host:port/dbname?sslmode=disable"
    exit 1
fi

echo "📊 Database URL: $DATABASE_URL"

# Function to check if psql is available
check_psql() {
    if ! command -v psql &> /dev/null; then
        echo "❌ ERROR: psql command not found"
        echo "Please install PostgreSQL client tools"
        exit 1
    fi
}

# Function to check if atlas is available
check_atlas() {
    if ! command -v atlas &> /dev/null; then
        echo "❌ ERROR: atlas command not found"
        echo "Please install Atlas CLI:"
        echo "  macOS: brew install ariga/tap/atlas"
        echo "  Linux: curl -sSf https://atlasgo.sh | sh"
        exit 1
    fi
}

# Function to test database connection
test_db_connection() {
    echo "🔌 Testing database connection..."
    if ! psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "❌ ERROR: Cannot connect to database"
        echo "Please check your DATABASE_URL and ensure the database is running"
        exit 1
    fi
    echo "✅ Database connection successful"
}

# Function to check if atlas_schema_revisions table exists and has data
check_atlas_revisions() {
    echo "🔍 Checking Atlas schema revisions..."
    
    # Check if table exists
    TABLE_EXISTS=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'atlas_schema_revisions';" 2>/dev/null | tr -d ' ')
    
    if [ "$TABLE_EXISTS" -eq "0" ]; then
        echo "📋 Atlas schema revisions table does not exist"
        return 1
    fi
    
    # Check if table has data
    REVISION_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM atlas_schema_revisions;" 2>/dev/null | tr -d ' ')
    
    if [ "$REVISION_COUNT" -eq "0" ]; then
        echo "📋 Atlas schema revisions table exists but is empty"
        return 1
    fi
    
    echo "✅ Atlas schema revisions table exists with $REVISION_COUNT entries"
    return 0
}

# Function to check if database has existing tables
check_existing_tables() {
    echo "🔍 Checking for existing database tables..."
    
    TABLE_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' AND table_name != 'atlas_schema_revisions';" 2>/dev/null | tr -d ' ')
    
    echo "📊 Found $TABLE_COUNT existing tables in public schema"
    return $TABLE_COUNT
}

# Function to get the latest migration file
get_latest_migration() {
    if [ ! -d "migrations" ]; then
        echo "❌ ERROR: migrations directory not found"
        exit 1
    fi
    
    LATEST_MIGRATION=$(ls -1 migrations/*.sql 2>/dev/null | tail -1 | xargs basename 2>/dev/null | sed 's/\.sql$//' 2>/dev/null || echo "")
    
    if [ -z "$LATEST_MIGRATION" ]; then
        echo "❌ ERROR: No migration files found in migrations directory"
        exit 1
    fi
    
    echo "📄 Latest migration file: $LATEST_MIGRATION"
    echo "$LATEST_MIGRATION"
}

# Function to set baseline
set_baseline() {
    local migration_version="$1"
    echo "🎯 Setting baseline to migration: $migration_version"
    
    if atlas migrate set "$migration_version" --url "$DATABASE_URL" --dir file://migrations; then
        echo "✅ Baseline set successfully to $migration_version"
    else
        echo "❌ ERROR: Failed to set baseline"
        exit 1
    fi
}

# Function to apply migrations normally
apply_migrations() {
    echo "🚀 Applying migrations..."
    
    if atlas migrate apply --url "$DATABASE_URL" --dir file://migrations; then
        echo "✅ Migrations applied successfully"
    else
        echo "❌ ERROR: Failed to apply migrations"
        exit 1
    fi
}

# Main execution
main() {
    echo "🏁 Starting Atlas baseline fix process..."
    
    # Check prerequisites
    check_psql
    check_atlas
    test_db_connection
    
    # Check current state
    if check_atlas_revisions; then
        echo "✅ Atlas is already properly initialized. Applying migrations normally..."
        apply_migrations
        echo "🎉 Migration process completed successfully!"
        return 0
    fi
    
    # Check if database has existing tables
    check_existing_tables
    TABLE_COUNT=$?
    
    if [ $TABLE_COUNT -gt 0 ]; then
        echo "🔧 Database has existing tables but no Atlas baseline. Setting up baseline..."
        LATEST_MIGRATION=$(get_latest_migration)
        set_baseline "$LATEST_MIGRATION"
        echo "✅ Baseline setup completed. Atlas now knows the current database state."
    else
        echo "🆕 Empty database detected. Applying migrations from scratch..."
        apply_migrations
    fi
    
    echo "🎉 Atlas baseline fix completed successfully!"
}

# Run main function
main "$@"
