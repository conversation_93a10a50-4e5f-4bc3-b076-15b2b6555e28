#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/agent?sslmode=disable"
echo "DATABASE_URL: $DATABASE_URL"

# Use the Atlas baseline fix script to handle migration properly
./scripts/fix-atlas-baseline.sh


# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
