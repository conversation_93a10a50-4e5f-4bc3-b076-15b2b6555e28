# Atlas Migration Baseline Fix

## Problem Description

When deploying the application, you may encounter this error:

```
Error: sql/migrate: connected database is not clean: found schema "public". baseline version or allow-dirty is required
```

### Root Cause

This error occurs because:

1. **GORM AutoMigrate** has already created database schema in production
2. **Atlas** doesn't know what version the current database is at
3. The `atlas_schema_revisions` table is either missing or empty
4. Atlas requires a **baseline version** to understand the current state

### Why This Happens

The project follows the **xbit-goback approach**:
- **Production**: Uses GORM AutoMigrate (automatic schema management)
- **Development**: Uses Atlas migrations (explicit schema control)

When Atlas tries to run migrations on a database that already has schema from GORM AutoMigrate, it doesn't know which migrations have already been "applied" conceptually.

## Solution

### Automatic Fix (Recommended)

The application now includes an automatic fix in the deployment process:

1. **Docker Deployment**: The `entrypoint.sh` automatically runs the fix script
2. **Manual Fix**: Run the baseline fix script manually

```bash
# Using Makefile
make db-fix-baseline

# Or run script directly
./scripts/fix-atlas-baseline.sh
```

### How the Fix Works

The fix script (`scripts/fix-atlas-baseline.sh`) does the following:

1. **Check Prerequisites**: Ensures `psql` and `atlas` are available
2. **Test Connection**: Verifies database connectivity
3. **Check Atlas State**: Determines if Atlas is already properly initialized
4. **Detect Scenario**:
   - If Atlas is already initialized → Apply migrations normally
   - If database has tables but no Atlas baseline → Set baseline to latest migration
   - If database is empty → Apply migrations from scratch

### Manual Steps (If Needed)

If you need to fix this manually:

```bash
# 1. Check current database state
psql "$DATABASE_URL" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';"

# 2. Check Atlas revisions
psql "$DATABASE_URL" -c "SELECT * FROM atlas_schema_revisions;" 2>/dev/null || echo "Table doesn't exist"

# 3. Set baseline to latest migration (if database has tables but no Atlas baseline)
LATEST_MIGRATION=$(ls -1 migrations/*.sql | tail -1 | xargs basename | sed 's/\.sql$//')
atlas migrate set "$LATEST_MIGRATION" --url "$DATABASE_URL" --dir file://migrations

# 4. Apply any remaining migrations
atlas migrate apply --url "$DATABASE_URL" --dir file://migrations
```

## Testing

Test the fix with:

```bash
# Test the migration fix
make db-test-migration-fix

# Or run test script directly
./scripts/test-migration-fix.sh
```

The test script will:
1. Show current database state
2. Simulate the issue by clearing Atlas revisions
3. Run the fix script
4. Verify the fix worked

## Files Modified

### New Files
- `scripts/fix-atlas-baseline.sh` - Main fix script
- `scripts/test-migration-fix.sh` - Test script
- `docs/ATLAS_MIGRATION_FIX.md` - This documentation

### Modified Files
- `entrypoint.sh` - Now uses the fix script automatically
- `Makefile` - Added `db-fix-baseline` and `db-test-migration-fix` targets
- `migrations/README.md` - Added troubleshooting section

## Usage in Different Environments

### Development
```bash
# Generate migration
make db-diff

# Apply migration locally
make db-apply

# Fix baseline if needed
make db-fix-baseline
```

### Docker Deployment
The fix is automatically applied in `entrypoint.sh` before starting the application.

### Production
In production, GORM AutoMigrate handles schema management automatically. The Atlas migrations are primarily for development reference.

## Prevention

To prevent this issue in the future:

1. **Always use the fix script** when setting up Atlas on existing databases
2. **Don't manually drop** the `atlas_schema_revisions` table
3. **Follow the migration workflow** described in `migrations/README.md`

## Troubleshooting

### Common Issues

1. **psql not found**: Install PostgreSQL client tools
2. **atlas not found**: Install Atlas CLI
3. **Database connection failed**: Check DATABASE_URL and database status
4. **Permission denied**: Ensure scripts are executable (`chmod +x scripts/*.sh`)

### Debug Commands

```bash
# Check Atlas status
atlas migrate status --url "$DATABASE_URL" --dir file://migrations

# Validate migrations
atlas migrate validate --dir file://migrations

# Check database tables
psql "$DATABASE_URL" -c "\dt"

# Check Atlas revisions
psql "$DATABASE_URL" -c "SELECT * FROM atlas_schema_revisions;"
```

## References

- [Atlas Migration Documentation](https://atlasgo.io/concepts/migration-directory)
- [Atlas Baseline Documentation](https://atlasgo.io/versioned/apply#existing-databases)
- [GORM AutoMigrate Documentation](https://gorm.io/docs/migration.html)
